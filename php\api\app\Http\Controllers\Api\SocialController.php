<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\SocialService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 社交互动与用户关系管理
 */
class SocialController extends Controller
{
    protected $socialService;

    public function __construct(SocialService $socialService)
    {
        $this->socialService = $socialService;
    }

    /**
     * @ApiTitle(关注用户)
     * @ApiSummary(关注或取消关注用户)
     * @ApiMethod(POST)
     * @ApiRoute(/api/social/follow)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="target_user_id", type="int", required=true, description="目标用户ID")
     * @ApiParams(name="action", type="string", required=true, description="操作类型：follow/unfollow")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "关注操作成功",
     *   "data": {
     *     "target_user_id": 456,
     *     "action": "follow",
     *     "is_following": true,
     *     "follower_count": 1251,
     *     "following_count": 340,
     *     "mutual_follow": false,
     *     "followed_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function follow(Request $request)
    {
        try {
            $rules = [
                'target_user_id' => 'required|integer|exists:users,id',
                'action' => 'required|string|in:follow,unfollow'
            ];

            $messages = [
                'target_user_id.required' => '目标用户ID不能为空',
                'target_user_id.exists' => '目标用户不存在',
                'action.required' => '操作类型不能为空',
                'action.in' => '操作类型必须是：follow、unfollow之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            if ($user->id === $request->target_user_id) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMETER,
                    'message' => '不能关注自己',
                    'data' => []
                ];
            }

            $followData = [
                'target_user_id' => $request->target_user_id,
                'action' => $request->action
            ];

            $result = $this->socialService->manageFollow($user->id, $followData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('关注操作失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '关注操作失败', []);
        }
    }

    /**
     * @ApiTitle(获取关注列表)
     * @ApiSummary(获取用户的关注和粉丝列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/social/follows)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="user_id", type="int", required=false, description="用户ID，默认为当前用户")
     * @ApiParams(name="type", type="string", required=false, description="列表类型：following/followers/mutual")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "list_type": "following",
     *     "user_id": 123,
     *     "users": [
     *       {
     *         "user_id": 456,
     *         "username": "创作达人",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "bio": "专注AI创作的设计师",
     *         "level": 20,
     *         "follower_count": 1250,
     *         "following_count": 340,
     *         "is_following": true,
     *         "is_followed_by": false,
     *         "mutual_follow": false,
     *         "followed_at": "2024-01-01 12:00:00",
     *         "recent_activity": "2小时前发布了新作品"
     *       }
     *     ],
     *     "statistics": {
     *       "total_following": 156,
     *       "total_followers": 234,
     *       "mutual_follows": 45,
     *       "new_followers_today": 3
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 156,
     *       "last_page": 8
     *     }
     *   }
     * })
     */
    public function follows(Request $request)
    {
        $rules = [
            'user_id' => 'sometimes|integer|exists:users,id',
            'type' => 'sometimes|string|in:following,followers,mutual',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $this->validateData($request->all(), $rules);

        $currentUser = auth()->user();
        $targetUserId = $request->get('user_id', $currentUser->id, []);
        
        $params = [
            'type' => $request->get('type', 'following'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->socialService->getFollowList($currentUser->id, $targetUserId, $params);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(点赞内容)
     * @ApiSummary(对作品或评论进行点赞)
     * @ApiMethod(POST)
     * @ApiRoute(/api/social/like)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="target_type", type="string", required=true, description="目标类型：publication/comment/template")
     * @ApiParams(name="target_id", type="int", required=true, description="目标ID")
     * @ApiParams(name="action", type="string", required=true, description="操作类型：like/unlike")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "点赞操作成功",
     *   "data": {
     *     "target_type": "publication",
     *     "target_id": 789,
     *     "action": "like",
     *     "is_liked": true,
     *     "like_count": 90,
     *     "liked_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function like(Request $request)
    {
        $rules = [
            'target_type' => 'required|string|in:publication,comment,template',
            'target_id' => 'required|integer',
            'action' => 'required|string|in:like,unlike'
        ];

        $messages = [
            'target_type.required' => '目标类型不能为空',
            'target_type.in' => '目标类型必须是：publication、comment、template之一',
            'target_id.required' => '目标ID不能为空',
            'action.required' => '操作类型不能为空',
            'action.in' => '操作类型必须是：like、unlike之一'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $likeData = [
            'target_type' => $request->target_type,
            'target_id' => $request->target_id,
            'action' => $request->action
        ];

        $result = $this->socialService->manageLike($user->id, $likeData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(评论内容)
     * @ApiSummary(对作品进行评论)
     * @ApiMethod(POST)
     * @ApiRoute(/api/social/comment)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="target_type", type="string", required=true, description="目标类型：publication/template")
     * @ApiParams(name="target_id", type="int", required=true, description="目标ID")
     * @ApiParams(name="content", type="string", required=true, description="评论内容")
     * @ApiParams(name="parent_id", type="int", required=false, description="父评论ID（回复评论时）")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "评论发布成功",
     *   "data": {
     *     "comment_id": 123,
     *     "target_type": "publication",
     *     "target_id": 789,
     *     "content": "这个作品很棒！",
     *     "parent_id": null,
     *     "author": {
     *       "user_id": 456,
     *       "username": "评论者",
     *       "avatar": "https://example.com/avatar.jpg"
     *     },
     *     "like_count": 0,
     *     "reply_count": 0,
     *     "is_liked": false,
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function comment(Request $request)
    {
        $rules = [
            'target_type' => 'required|string|in:publication,template',
            'target_id' => 'required|integer',
            'content' => 'required|string|min:1|max:1000',
            'parent_id' => 'sometimes|integer|exists:comments,id'
        ];

        $messages = [
            'target_type.required' => '目标类型不能为空',
            'target_type.in' => '目标类型必须是：publication、template之一',
            'target_id.required' => '目标ID不能为空',
            'content.required' => '评论内容不能为空',
            'content.min' => '评论内容至少1个字符',
            'content.max' => '评论内容不能超过1000个字符',
            'parent_id.exists' => '父评论不存在'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $commentData = [
            'target_type' => $request->target_type,
            'target_id' => $request->target_id,
            'content' => $request->content,
            'parent_id' => $request->get('parent_id')
        ];

        $result = $this->socialService->createComment($user->id, $commentData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(获取评论列表)
     * @ApiSummary(获取指定内容的评论列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/social/comments)
     * @ApiParams(name="target_type", type="string", required=true, description="目标类型：publication/template")
     * @ApiParams(name="target_id", type="int", required=true, description="目标ID")
     * @ApiParams(name="sort", type="string", required=false, description="排序方式：latest/oldest/popular")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "target_type": "publication",
     *     "target_id": 789,
     *     "comments": [
     *       {
     *         "comment_id": 123,
     *         "content": "这个作品很棒！",
     *         "author": {
     *           "user_id": 456,
     *           "username": "评论者",
     *           "avatar": "https://example.com/avatar.jpg",
     *           "level": 15
     *         },
     *         "like_count": 5,
     *         "reply_count": 2,
     *         "is_liked": false,
     *         "created_at": "2024-01-01 12:00:00",
     *         "replies": [
     *           {
     *             "comment_id": 124,
     *             "content": "我也觉得！",
     *             "author": {
     *               "user_id": 789,
     *               "username": "回复者",
     *               "avatar": "https://example.com/avatar2.jpg"
     *             },
     *             "like_count": 1,
     *             "is_liked": false,
     *             "created_at": "2024-01-01 12:05:00"
     *           }
     *         ]
     *       }
     *     ],
     *     "statistics": {
     *       "total_comments": 45,
     *       "total_replies": 23,
     *       "average_rating": 4.5
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 45,
     *       "last_page": 3
     *     }
     *   }
     * })
     */
    public function comments(Request $request)
    {
        $rules = [
            'target_type' => 'required|string|in:publication,template',
            'target_id' => 'required|integer',
            'sort' => 'sometimes|string|in:latest,oldest,popular',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $this->validateData($request->all(), $rules);

        $userId = auth()->id(); // 可能为null（未登录用户）
        
        $params = [
            'target_type' => $request->target_type,
            'target_id' => $request->target_id,
            'sort' => $request->get('sort', 'latest'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->socialService->getComments($userId, $params);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(分享内容)
     * @ApiSummary(分享作品到社交平台)
     * @ApiMethod(POST)
     * @ApiRoute(/api/social/share)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="target_type", type="string", required=true, description="目标类型：publication/template")
     * @ApiParams(name="target_id", type="int", required=true, description="目标ID")
     * @ApiParams(name="platform", type="string", required=true, description="分享平台：internal/wechat/weibo/twitter")
     * @ApiParams(name="message", type="string", required=false, description="分享消息")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "分享成功",
     *   "data": {
     *     "share_id": 123,
     *     "target_type": "publication",
     *     "target_id": 789,
     *     "platform": "internal",
     *     "share_url": "https://example.com/share/123",
     *     "share_count": 15,
     *     "shared_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function share(Request $request)
    {
        $rules = [
            'target_type' => 'required|string|in:publication,template',
            'target_id' => 'required|integer',
            'platform' => 'required|string|in:internal,wechat,weibo,twitter,facebook',
            'message' => 'sometimes|string|max:500'
        ];

        $messages = [
            'target_type.required' => '目标类型不能为空',
            'target_type.in' => '目标类型必须是：publication、template之一',
            'target_id.required' => '目标ID不能为空',
            'platform.required' => '分享平台不能为空',
            'platform.in' => '分享平台必须是：internal、wechat、weibo、twitter、facebook之一',
            'message.max' => '分享消息不能超过500个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $shareData = [
            'target_type' => $request->target_type,
            'target_id' => $request->target_id,
            'platform' => $request->platform,
            'message' => $request->get('message')
        ];

        $result = $this->socialService->shareContent($user->id, $shareData);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(获取社交动态)
     * @ApiSummary(获取关注用户的动态信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/social/feed)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="动态类型：all/publications/follows/likes/comments")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "feed_type": "all",
     *     "activities": [
     *       {
     *         "activity_id": 123,
     *         "type": "publication_created",
     *         "actor": {
     *           "user_id": 456,
     *           "username": "创作者",
     *           "avatar": "https://example.com/avatar.jpg"
     *         },
     *         "action": "发布了新作品",
     *         "target": {
     *           "type": "publication",
     *           "id": 789,
     *           "title": "精彩的AI故事",
     *           "thumbnail": "https://example.com/thumb.jpg"
     *         },
     *         "metadata": {
     *           "like_count": 25,
     *           "comment_count": 8
     *         },
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "unread_count": 5,
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 156,
     *       "last_page": 8
     *     }
     *   }
     * })
     */
    public function feed(Request $request)
    {
        $rules = [
            'type' => 'sometimes|string|in:all,publications,follows,likes,comments',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:50'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $params = [
            'type' => $request->get('type', 'all'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->socialService->getSocialFeed($user->id, $params);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(获取通知)
     * @ApiSummary(获取用户的社交通知)
     * @ApiMethod(GET)
     * @ApiRoute(/api/social/notifications)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="通知类型：all/likes/comments/follows/mentions")
     * @ApiParams(name="status", type="string", required=false, description="状态：all/unread/read")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "notifications": [
     *       {
     *         "notification_id": 123,
     *         "type": "like",
     *         "title": "有人点赞了你的作品",
     *         "message": "用户"创作者"点赞了你的作品"精彩的AI故事"",
     *         "actor": {
     *           "user_id": 456,
     *           "username": "创作者",
     *           "avatar": "https://example.com/avatar.jpg"
     *         },
     *         "target": {
     *           "type": "publication",
     *           "id": 789,
     *           "title": "精彩的AI故事"
     *         },
     *         "is_read": false,
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "unread_count": 8,
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 45,
     *       "last_page": 3
     *     }
     *   }
     * })
     */
    public function notifications(Request $request)
    {
        $rules = [
            'type' => 'sometimes|string|in:all,likes,comments,follows,mentions',
            'status' => 'sometimes|string|in:all,unread,read',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        
        $params = [
            'type' => $request->get('type', 'all'),
            'status' => $request->get('status', 'all'),
            'page' => $request->get('page', 1),
            'per_page' => $request->get('per_page', 20)
        ];

        $result = $this->socialService->getNotifications($user->id, $params);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }

    /**
     * @ApiTitle(标记通知已读)
     * @ApiSummary(标记通知为已读状态)
     * @ApiMethod(POST)
     * @ApiRoute(/api/social/mark-notifications-read)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="notification_ids", type="array", required=false, description="通知ID数组，为空则标记所有未读")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "通知标记成功",
     *   "data": {
     *     "marked_count": 8,
     *     "remaining_unread": 0,
     *     "marked_at": "2024-01-01 12:30:00"
     *   }
     * })
     */
    public function markNotificationsRead(Request $request)
    {
        $rules = [
            'notification_ids' => 'sometimes|array',
            'notification_ids.*' => 'integer'
        ];

        $this->validateData($request->all(), $rules);

        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];
        $notificationIds = $request->get('notification_ids', []);

        $result = $this->socialService->markNotificationsRead($user->id, $notificationIds);

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    }
}
