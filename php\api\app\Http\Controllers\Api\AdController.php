<?php

namespace App\Http\Controllers\Api;

use Carbon\Carbon;
use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;
use App\Helpers\StringHelper;
use App\Enums\UserAdStateEnum;
use App\Services\AuthService;
use App\Services\WebSocketTokenService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;

/**
 * 广告投放管理与效果追踪（wss）
 */
class AdController extends Controller
{
    /**
     * @ApiTitle(广告开始)
     * @ApiSummary()
     * @ApiMethod(POST)
     * @ApiRoute (ad.store)
     * @ApiParams(name="ad_id", type="int", required=true, description="广告ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data['id']", type="string", required=true, description="广告记录ID")
     * @ApiReturn({
    "code": 200,
    "message": "success",
    "data": {
    "id": "584b4614-d127-4e49-a5ba-c76f86f41078"
    }
    })
     */
    public function ad_store(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'ad_id' => 'required|integer'
        ];
        $messages = [
            'ad_id.required' => '广告ID不能为空',
            'ad_id.integer' => '广告ID必须是整数'
        ];

        // 数据验证
        $this->validateData($request->all(), $rules, $messages, []);

        $uuid = StringHelper::uuid();

        $job_data = [
            'user_id' => $user->id,
            'uuid' => $uuid,
            'ad_id' => $request['ad_id'],
        ];
        $now = Carbon::now()->toDateTimeString();

        // 直接同步处理，避免Redis队列问题
        try {
            (new \App\Services\AdService)->store($job_data, $now);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Ad store error: ' . $e->getMessage());
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告记录保存失败');
        }

        $data = [
            'id' => $uuid
        ];
        return $this->successResponse($data, 'success');
    }

    /**
     * @ApiTitle(广告结束)
     * @ApiSummary()
     * @ApiMethod(POST)
     * @ApiRoute (ad.update)
     * @ApiParams(name="id", type="string", required=true, description="广告记录ID")
     * @ApiParams(name="time_length", type="int", required=true, description="时长（秒）")
     * @ApiParams(name="state", type="int", required=true, description="状态。0：进行中，1：加载失败，2：开始播放，3：离开，4：超时，5：结束")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="array", required=true, description="数据")
     * @ApiReturn({
    "code": 200,
    "message": "success",
    "data": []
    })
     */
    public function ad_update(Request $request)
    {
        // 使用AuthService进行认证
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        $rules = [
            'id' => 'required|max:40',
            'time_length' => 'required|integer|gte:0',
            'state' => 'required|in:'.UserAdStateEnum::getKeysString()
        ];
        $messages = [
            'id.required' => '广告记录ID不能为空',
            'id.max' => '广告记录ID超过40个字符',
            'time_length.required' => '时长不能为空',
            'time_length.integer' => '时长必须是整数',
            'time_length.gte' => '时长不能小于0',
            'state.required' => '状态不能为空',
            'state.in' => '状态无效'
        ];
        $this->validateData($request->all(), $rules, $messages, []);
        $job_data = [
            'user_id' => $user->id,
            'uuid' => $request['id'],
            'time_length' => $request['time_length'],
            'state' => $request['state']
        ];
        $now = Carbon::now()->toDateTimeString();

        // 直接同步处理，避免Redis队列问题
        try {
            (new \App\Services\AdService)->update($job_data, $now);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Ad update error: ' . $e->getMessage());
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告记录更新失败');
        }

        return $this->successResponse([], 'success');
    }

    /**
     * WebSocket广告开始
     * 处理 ad.store 事件
     *
     * @param array $requests WebSocket请求数据，包含token和业务参数
     * @return array
     */
    public function store(array $requests)
    {
        // 验证token
        $tokenValidation = WebSocketTokenService::validateToken($requests);

        if (!$tokenValidation['valid']) {
            WebSocketTokenService::logWebSocketOperation('ad.store', 0, $requests, 'error');
            return WebSocketTokenService::generateErrorResponse($tokenValidation['error'], 'ad.store');
        }

        $user_id = $tokenValidation['user_id'];

        try {
            $uuid = StringHelper::uuid();

            $job_data = [
                'user_id' => $user_id,
                'uuid' => $uuid,
                'ad_id' => $requests['ad_id'] ?? null,
            ];
            $now = Carbon::now()->toDateTimeString();

            // 使用AdService处理广告逻辑
            (new \App\Services\AdService)->store($job_data, $now);

            // 记录成功日志
            WebSocketTokenService::logWebSocketOperation('ad.store', $user_id, $job_data);

            return WebSocketTokenService::generateSuccessResponse(['id' => $uuid], 'ad.store');

        } catch (\Exception $e) {
            WebSocketTokenService::logWebSocketOperation('ad.store', $user_id, $requests, 'error');
            return WebSocketTokenService::generateErrorResponse([
                'code' => ApiCodeEnum::CONTROLLER_ERROR,
                'message' => '广告开始失败: ' . $e->getMessage()
            ], 'ad.store');
        }
    }

    /**
     * WebSocket广告结束
     * 处理 ad.update 事件
     *
     * @param array $requests WebSocket请求数据，包含token和业务参数
     * @return array
     */
    public function update(array $requests)
    {
        // 验证token
        $tokenValidation = WebSocketTokenService::validateToken($requests);

        if (!$tokenValidation['valid']) {
            WebSocketTokenService::logWebSocketOperation('ad.update', 0, $requests, 'error');
            return WebSocketTokenService::generateErrorResponse($tokenValidation['error'], 'ad.update');
        }

        $user_id = $tokenValidation['user_id'];

        try {
            $job_data = [
                'user_id' => $user_id,
                'uuid' => $requests['id'] ?? null,
                'time_length' => $requests['time_length'] ?? null,
                'state' => $requests['state'] ?? null,
            ];
            $now = Carbon::now()->toDateTimeString();

            // 使用AdService处理广告逻辑
            (new \App\Services\AdService)->update($job_data, $now);

            // 记录成功日志
            WebSocketTokenService::logWebSocketOperation('ad.update', $user_id, $job_data);

            return WebSocketTokenService::generateSuccessResponse([], 'ad.update');

        } catch (\Exception $e) {
            WebSocketTokenService::logWebSocketOperation('ad.update', $user_id, $requests, 'error');
            return WebSocketTokenService::generateErrorResponse([
                'code' => ApiCodeEnum::CONTROLLER_ERROR,
                'message' => '广告结束失败: ' . $e->getMessage()
            ], 'ad.update');
        }
    }
}